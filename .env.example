# Database Configuration
MONGODB_URI=mongodb://localhost:27017/wedding-photography
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/wedding-photography

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD_HASH=your-bcrypt-hashed-password-here

# Email Configuration (for contact form)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_FROM=<EMAIL>
CONTACT_EMAIL=<EMAIL>

# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-nextauth-secret-here

# Optional: Analytics and Tracking
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
GOOGLE_TAG_MANAGER_ID=GTM-XXXXXXX

# Optional: Social Media Integration
INSTAGRAM_ACCESS_TOKEN=your-instagram-token
FACEBOOK_PAGE_ID=your-facebook-page-id

# Optional: Image Storage (if using cloud storage)
CLOUDINARY_CLOUD_NAME=your-cloudinary-name
CLOUDINARY_API_KEY=your-cloudinary-key
CLOUDINARY_API_SECRET=your-cloudinary-secret

# Optional: Payment Integration (for booking deposits)
STRIPE_PUBLISHABLE_KEY=pk_test_your-stripe-key
STRIPE_SECRET_KEY=sk_test_your-stripe-secret
