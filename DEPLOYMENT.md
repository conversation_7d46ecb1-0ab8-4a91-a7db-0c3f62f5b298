# Deployment Guide

This guide covers deploying your wedding photography website to various platforms.

## 🚀 Vercel (Recommended)

Vercel is the easiest way to deploy Next.js applications.

### Step 1: Prepare Your Repository
```bash
git add .
git commit -m "Initial commit"
git push origin main
```

### Step 2: Deploy to Vercel
1. Visit [vercel.com](https://vercel.com) and sign up
2. Click "New Project"
3. Import your GitHub repository
4. Configure project settings:
   - Framework Preset: Next.js
   - Build Command: `npm run build`
   - Output Directory: `.next`

### Step 3: Environment Variables
Add these in Vercel dashboard under Settings > Environment Variables:

```
MONGODB_URI=mongodb+srv://username:<EMAIL>/wedding-photography
JWT_SECRET=your-super-secret-jwt-key-make-it-long-and-random
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD_HASH=your-bcrypt-hashed-password
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-gmail-app-password
SMTP_FROM=<EMAIL>
CONTACT_EMAIL=<EMAIL>
NEXTAUTH_URL=https://yourdomain.com
```

### Step 4: Custom Domain
1. Go to Settings > Domains
2. Add your custom domain
3. Configure DNS records as instructed

## 🌐 Netlify

### Step 1: Build Settings
- Build command: `npm run build`
- Publish directory: `.next`
- Node version: 18

### Step 2: Environment Variables
Add the same environment variables as Vercel in Site Settings > Environment Variables.

### Step 3: Deploy
1. Connect your GitHub repository
2. Configure build settings
3. Deploy

## 🌊 DigitalOcean App Platform

### Step 1: Create App
1. Visit DigitalOcean App Platform
2. Create new app from GitHub repository

### Step 2: Configure
- Runtime: Node.js
- Build command: `npm run build`
- Run command: `npm start`

### Step 3: Environment Variables
Add environment variables in App Settings.

## 🗄️ Database Setup

### MongoDB Atlas (Recommended)
1. Create account at [mongodb.com](https://mongodb.com)
2. Create new cluster
3. Create database user
4. Whitelist IP addresses (0.0.0.0/0 for all)
5. Get connection string
6. Update `MONGODB_URI` environment variable

### Local MongoDB
```bash
# Install MongoDB
brew install mongodb/brew/mongodb-community

# Start MongoDB
brew services start mongodb/brew/mongodb-community

# Connection string
MONGODB_URI=mongodb://localhost:27017/wedding-photography
```

## 📧 Email Configuration

### Gmail Setup
1. Enable 2-Factor Authentication
2. Generate App Password:
   - Google Account > Security > App passwords
   - Select app: Mail
   - Select device: Other (custom name)
   - Copy the generated password

```env
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-16-character-app-password
```

### SendGrid Setup
1. Create SendGrid account
2. Create API key
3. Verify sender identity

```env
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
```

## 🔐 Security Setup

### Generate Admin Password Hash
```bash
node -e "console.log(require('bcryptjs').hashSync('your-admin-password', 12))"
```

### JWT Secret
Generate a strong JWT secret:
```bash
node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"
```

## 🎯 Domain & DNS

### Custom Domain Setup
1. Purchase domain from registrar
2. Point nameservers to your hosting provider
3. Configure DNS records:
   - A record: @ → your-server-ip
   - CNAME: www → yourdomain.com

### SSL Certificate
Most platforms (Vercel, Netlify) provide automatic SSL certificates.

## 📊 Analytics Setup

### Google Analytics
1. Create GA4 property
2. Get Measurement ID
3. Add to environment variables:
```env
GOOGLE_ANALYTICS_ID=G-XXXXXXXXXX
```

### Google Search Console
1. Add property for your domain
2. Verify ownership
3. Submit sitemap: `https://yourdomain.com/sitemap.xml`

## 🚀 Performance Optimization

### Image Optimization
- Use WebP format for images
- Implement lazy loading
- Optimize image sizes for different devices

### Caching
- Static assets cached automatically
- API responses can be cached
- Use CDN for global distribution

## 🔍 SEO Checklist

- [ ] Custom domain configured
- [ ] SSL certificate active
- [ ] Sitemap submitted to Google
- [ ] Meta tags configured
- [ ] Open Graph tags set
- [ ] Structured data implemented
- [ ] Page speed optimized
- [ ] Mobile-friendly design

## 🛠️ Troubleshooting

### Common Issues

**Build Errors:**
- Check Node.js version (18+)
- Verify all dependencies installed
- Check TypeScript errors

**Database Connection:**
- Verify MongoDB URI format
- Check network access in MongoDB Atlas
- Ensure database user has correct permissions

**Email Not Working:**
- Verify SMTP credentials
- Check app password for Gmail
- Test with email service provider

**Admin Login Issues:**
- Verify password hash generation
- Check JWT secret configuration
- Ensure admin email is correct

### Debug Mode
Enable debug logging:
```env
DEBUG=true
NODE_ENV=development
```

## 📞 Support

For deployment issues:
1. Check the logs in your deployment platform
2. Verify all environment variables
3. Test locally first
4. Check database connectivity

## 🔄 Updates & Maintenance

### Regular Updates
- Update dependencies monthly
- Monitor security advisories
- Backup database regularly
- Test functionality after updates

### Content Updates
- Use admin panel for content management
- Update images through admin interface
- Monitor contact form submissions
- Review and approve testimonials

---

**Your wedding photography website is now ready to capture hearts online! 💕**
