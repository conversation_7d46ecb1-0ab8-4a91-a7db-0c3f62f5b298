# Eternal Moments - Wedding Photography Website

A beautiful, full-stack wedding photography business website built with Next.js, TypeScript, Tailwind CSS, and MongoDB. Features a romantic design, portfolio gallery, contact forms, testimonials, and admin panel.

## ✨ Features

- **Modern Design**: Romantic color palette with elegant typography and smooth animations
- **Responsive Layout**: Optimized for all devices (desktop, tablet, mobile)
- **Portfolio Gallery**: Interactive image gallery with lightbox and category filtering
- **Contact System**: Contact form with email notifications and inquiry management
- **Testimonials**: Client review system with approval workflow
- **Admin Panel**: Secure admin interface for content management
- **SEO Optimized**: Meta tags, structured data, and performance optimized
- **Email Integration**: Automated email notifications for inquiries

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ installed
- MongoDB database (local or MongoDB Atlas)
- Email service (Gmail, SendGrid, etc.)

### Installation

1. **Clone and setup the project:**
```bash
git clone <your-repo-url>
cd wedding-photography
npm install
```

2. **Environment Configuration:**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your configuration:
```env
# Database
MONGODB_URI=mongodb://localhost:27017/wedding-photography

# Authentication
JWT_SECRET=your-super-secret-jwt-key-here
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD_HASH=your-bcrypt-hashed-password

# Email (Gmail example)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
CONTACT_EMAIL=<EMAIL>
```

3. **Generate Admin Password Hash:**
```bash
node -e "console.log(require('bcryptjs').hashSync('your-admin-password', 12))"
```

4. **Start Development Server:**
```bash
npm run dev
```

Visit `http://localhost:3000` to see your website!

## 📁 Project Structure

```
wedding-photography/
├── src/
│   ├── app/                 # Next.js App Router pages
│   │   ├── about/          # About page
│   │   ├── admin/          # Admin panel
│   │   ├── api/            # API routes
│   │   ├── contact/        # Contact page
│   │   ├── portfolio/      # Portfolio gallery
│   │   ├── services/       # Services & pricing
│   │   ├── testimonials/   # Client testimonials
│   │   └── globals.css     # Global styles
│   ├── components/         # Reusable components
│   │   ├── common/         # Shared components
│   │   ├── contact/        # Contact form components
│   │   ├── home/           # Homepage components
│   │   ├── layout/         # Layout components
│   │   └── portfolio/      # Gallery components
│   ├── lib/                # Utility functions
│   │   ├── auth.ts         # Authentication helpers
│   │   ├── mongodb.ts      # Database connection
│   │   └── seo.ts          # SEO utilities
│   └── models/             # Database models
├── public/                 # Static assets
├── tailwind.config.ts      # Tailwind configuration
└── package.json           # Dependencies
```

## 🎨 Customization

### Brand Colors
Edit `tailwind.config.ts` to customize the color palette:
```typescript
colors: {
  primary: { /* Your primary colors */ },
  blush: { /* Your accent colors */ },
  gold: { /* Your highlight colors */ },
}
```

### Content Updates
- **Homepage**: Edit components in `src/components/home/<USER>
- **About Page**: Update `src/app/about/page.tsx`
- **Services**: Modify `src/app/services/page.tsx`
- **Contact Info**: Update `src/components/layout/Footer.tsx`

### Images
Replace placeholder images with your own:
- Use high-quality wedding photography images
- Optimize images for web (WebP format recommended)
- Update image URLs in components

## 🚀 Deployment

### Vercel (Recommended)

1. **Push to GitHub:**
```bash
git add .
git commit -m "Initial commit"
git push origin main
```

2. **Deploy to Vercel:**
- Visit [vercel.com](https://vercel.com)
- Import your GitHub repository
- Add environment variables in Vercel dashboard
- Deploy!

### Environment Variables for Production
Add these in your deployment platform:
```
MONGODB_URI=mongodb+srv://...
JWT_SECRET=your-production-secret
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD_HASH=your-hashed-password
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
CONTACT_EMAIL=<EMAIL>
```

### Other Deployment Options

**Netlify:**
- Connect GitHub repository
- Build command: `npm run build`
- Publish directory: `.next`

**DigitalOcean App Platform:**
- Connect repository
- Configure environment variables
- Deploy with automatic builds

## 📧 Email Setup

### Gmail Setup
1. Enable 2-factor authentication
2. Generate an App Password
3. Use App Password in `SMTP_PASS`

### SendGrid Setup
```env
SMTP_HOST=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USER=apikey
SMTP_PASS=your-sendgrid-api-key
```

## 🔐 Admin Panel

Access the admin panel at `/admin`:
- **Default Email**: Set in `ADMIN_EMAIL`
- **Password**: Set via `ADMIN_PASSWORD_HASH`

Admin features:
- View contact form submissions
- Manage testimonials
- Upload gallery images
- Update services and pricing

## 🛠 Development

### Available Scripts
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
```

### Database Models
- **Contact**: Contact form submissions
- **Testimonial**: Client reviews
- **GalleryImage**: Portfolio images
- **Service**: Service packages

## 📱 Features Overview

### Homepage
- Hero section with rotating background images
- Featured work gallery
- About preview with photographer bio
- Services overview with pricing

### Portfolio
- Category-filtered image gallery
- Lightbox with navigation
- Responsive masonry layout
- Image optimization

### Contact System
- Comprehensive contact form
- Email notifications
- Admin dashboard for inquiries
- Form validation and error handling

### Admin Panel
- Secure authentication
- Content management interface
- Statistics dashboard
- Responsive admin design

## 🎯 SEO Features

- Meta tags and Open Graph
- Structured data (JSON-LD)
- Sitemap generation
- Image optimization
- Fast loading times
- Mobile-first design

## 🤝 Support

For questions or customization help:
- Check the documentation
- Review the code comments
- Test thoroughly before deployment

## 📄 License

This project is created for wedding photography businesses. Customize and use as needed for your business.

---

**Built with ❤️ for wedding photographers who want to showcase their work beautifully online.**
