# Quick Setup Guide

Get your wedding photography website running in minutes!

## 🚀 Prerequisites

Before you begin, make sure you have:
- **Node.js 18+** installed ([Download here](https://nodejs.org/))
- **MongoDB** database (local or [MongoDB Atlas](https://mongodb.com))
- **Email account** for contact form (Gmail recommended)

## ⚡ Quick Start

### 1. Install Node.js
If you don't have Node.js installed:
- Visit [nodejs.org](https://nodejs.org/)
- Download and install the LTS version
- Verify installation: `node --version`

### 2. Setup Project
```bash
# Navigate to your project folder
cd wedding-photography

# Install dependencies
npm install
```

### 3. Environment Configuration
```bash
# Copy environment template
cp .env.example .env.local
```

Edit `.env.local` with your settings:
```env
# Database (MongoDB Atlas recommended)
MONGODB_URI=mongodb+srv://username:<EMAIL>/wedding-photography

# Admin Access
JWT_SECRET=your-super-secret-jwt-key-make-it-very-long-and-random
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD_HASH=will-be-generated-below

# Email Settings (Gmail example)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-gmail-app-password
CONTACT_EMAIL=<EMAIL>
```

### 4. Database Setup

**Option A: MongoDB Atlas (Recommended)**
1. Go to [mongodb.com](https://mongodb.com) and create free account
2. Create new cluster (free tier available)
3. Create database user with read/write permissions
4. Get connection string and update `MONGODB_URI`

**Option B: Local MongoDB**
```bash
# Install MongoDB locally
brew install mongodb/brew/mongodb-community  # macOS
# or follow instructions for your OS

# Start MongoDB
brew services start mongodb/brew/mongodb-community

# Use local connection
MONGODB_URI=mongodb://localhost:27017/wedding-photography
```

### 5. Email Setup (Gmail)
1. Enable 2-Factor Authentication on your Gmail account
2. Generate App Password:
   - Google Account → Security → App passwords
   - Select "Mail" and "Other (custom name)"
   - Copy the 16-character password
3. Update `.env.local`:
   ```env
   SMTP_USER=<EMAIL>
   SMTP_PASS=your-16-character-app-password
   ```

### 6. Generate Admin Password
```bash
# Generate password hash
node -e "console.log(require('bcryptjs').hashSync('your-admin-password', 12))"

# Copy the output to .env.local
ADMIN_PASSWORD_HASH=the-generated-hash
```

### 7. Seed Sample Data
```bash
# Add sample content to your database
npm run seed
```

### 8. Start Development Server
```bash
npm run dev
```

Visit `http://localhost:3000` - Your website is ready! 🎉

## 🔐 Admin Access

- **URL**: `http://localhost:3000/admin`
- **Email**: The email you set in `ADMIN_EMAIL`
- **Password**: The password you used to generate the hash

## 📱 Test Your Website

1. **Homepage**: Check hero section and navigation
2. **Portfolio**: Browse image gallery
3. **Contact Form**: Submit a test inquiry
4. **Admin Panel**: Login and check dashboard
5. **Responsive**: Test on mobile devices

## 🚀 Deploy to Production

### Vercel (Easiest)
1. Push code to GitHub
2. Visit [vercel.com](https://vercel.com)
3. Import your repository
4. Add environment variables
5. Deploy!

### Environment Variables for Production
Add these in your deployment platform:
```
MONGODB_URI=your-production-mongodb-uri
JWT_SECRET=your-production-jwt-secret
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD_HASH=your-production-password-hash
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-gmail-app-password
CONTACT_EMAIL=<EMAIL>
NEXTAUTH_URL=https://yourdomain.com
```

## 🎨 Customization

### Update Content
- **Business Name**: Search and replace "Eternal Moments" in all files
- **Contact Info**: Update footer and contact page
- **About Section**: Edit `src/app/about/page.tsx`
- **Services**: Modify `src/app/services/page.tsx`

### Change Colors
Edit `tailwind.config.ts`:
```typescript
colors: {
  primary: {
    // Your brand colors
    500: '#your-primary-color',
    600: '#your-primary-dark',
  },
  blush: {
    // Your accent colors
    500: '#your-accent-color',
  }
}
```

### Add Your Images
Replace placeholder images with your photography:
- Use high-quality images (1200px+ width)
- Optimize for web (WebP format recommended)
- Update image URLs in components

## 🛠️ Troubleshooting

### Common Issues

**"Module not found" errors:**
```bash
rm -rf node_modules package-lock.json
npm install
```

**Database connection failed:**
- Check MongoDB URI format
- Verify database user permissions
- Ensure network access (whitelist IP in Atlas)

**Email not sending:**
- Verify Gmail app password (not regular password)
- Check SMTP settings
- Test with a different email service

**Admin login not working:**
- Verify password hash generation
- Check JWT secret is set
- Ensure admin email matches

### Get Help
- Check the detailed `README.md`
- Review `DEPLOYMENT.md` for deployment issues
- Verify all environment variables are set correctly

## ✅ Launch Checklist

Before going live:
- [ ] Test all pages and functionality
- [ ] Update all placeholder content
- [ ] Add your own images
- [ ] Test contact form
- [ ] Verify admin panel access
- [ ] Set up custom domain
- [ ] Configure SSL certificate
- [ ] Submit sitemap to Google
- [ ] Test on mobile devices

## 🎯 Next Steps

1. **Content**: Add your real photography portfolio
2. **SEO**: Optimize for local search terms
3. **Analytics**: Add Google Analytics
4. **Social**: Connect Instagram feed
5. **Booking**: Consider adding online booking system

---

**Congratulations! Your wedding photography website is ready to capture hearts online! 💕**

Need help? Check the detailed documentation in `README.md` and `DEPLOYMENT.md`.
