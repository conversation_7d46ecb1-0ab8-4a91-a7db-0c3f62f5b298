const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');

// Import models (adjust paths as needed)
const GalleryImage = require('../src/models/GalleryImage');
const Service = require('../src/models/Service');
const Testimonial = require('../src/models/Testimonial');

// Sample data
const sampleGalleryImages = [
  {
    title: 'Romantic Garden Ceremony',
    description: 'Beautiful outdoor ceremony in a botanical garden',
    imageUrl: 'https://images.unsplash.com/photo-1519741497674-611481863552?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'ceremony',
    tags: ['outdoor', 'garden', 'romantic'],
    isFeature: true,
    order: 1,
    altText: 'Bride and groom exchanging vows in garden ceremony'
  },
  {
    title: 'Wedding Ring Details',
    description: 'Elegant wedding rings on silk fabric',
    imageUrl: 'https://images.unsplash.com/photo-1465495976277-4387d4b0e4a6?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'details',
    tags: ['rings', 'details', 'elegant'],
    isFeature: true,
    order: 2,
    altText: 'Beautiful wedding rings on white silk fabric'
  },
  {
    title: 'Couple Portrait Session',
    description: 'Intimate portrait session during golden hour',
    imageUrl: 'https://images.unsplash.com/photo-1511285560929-80b456fea0bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'portraits',
    tags: ['portraits', 'golden hour', 'intimate'],
    isFeature: true,
    order: 3,
    altText: 'Romantic couple portrait during golden hour'
  },
  {
    title: 'Reception Dance Floor',
    description: 'Joyful moments on the dance floor',
    imageUrl: 'https://images.unsplash.com/photo-1606216794074-735e91aa2c92?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80',
    category: 'reception',
    tags: ['reception', 'dancing', 'celebration'],
    isFeature: false,
    order: 4,
    altText: 'Couple dancing at their wedding reception'
  }
];

const sampleServices = [
  {
    name: 'Full Wedding Day',
    description: 'Complete wedding day coverage from getting ready to reception with professional editing and online gallery.',
    price: 2500,
    duration: '8-10 hours',
    includes: [
      'Complimentary engagement session',
      '500+ professionally edited high-resolution photos',
      'Online gallery with download & sharing options',
      'Print release for personal use',
      'Second photographer included',
      'USB drive with all images'
    ],
    isPopular: true,
    order: 1,
    isActive: true
  },
  {
    name: 'Elopement Package',
    description: 'Perfect for intimate ceremonies and small celebrations with quick turnaround.',
    price: 1200,
    duration: '3-4 hours',
    includes: [
      'Intimate ceremony coverage',
      '150+ professionally edited photos',
      'Online gallery with sharing options',
      'Print release included',
      'Quick turnaround (1 week)'
    ],
    isPopular: false,
    order: 2,
    isActive: true
  },
  {
    name: 'Engagement Session',
    description: 'Pre-wedding couple photography session perfect for save-the-dates.',
    price: 450,
    duration: '1-2 hours',
    includes: [
      'Location of your choice',
      '75+ professionally edited photos',
      'Online gallery for sharing',
      'Perfect for save-the-dates',
      'One outfit change included'
    ],
    isPopular: false,
    order: 3,
    isActive: true
  }
];

const sampleTestimonials = [
  {
    name: 'Sarah & Michael',
    email: '<EMAIL>',
    message: 'Sarah captured our wedding day so beautifully! Every photo tells a story and brings back all the emotions from that perfect day. Her attention to detail and ability to capture candid moments is incredible.',
    rating: 5,
    weddingDate: new Date('2023-09-15'),
    isApproved: true
  },
  {
    name: 'Emma & James',
    email: '<EMAIL>',
    message: 'Working with Sarah was an absolute dream. She made us feel so comfortable during our engagement session and wedding day. The photos are stunning and we\'ve received so many compliments.',
    rating: 5,
    weddingDate: new Date('2023-07-22'),
    isApproved: true
  },
  {
    name: 'Lisa & David',
    email: '<EMAIL>',
    message: 'Sarah is not just a photographer, she\'s an artist. Our wedding photos are like pieces of art that we\'ll treasure forever. Her style is exactly what we were looking for - romantic, timeless, and elegant.',
    rating: 5,
    weddingDate: new Date('2023-06-10'),
    isApproved: true
  }
];

async function seedDatabase() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/wedding-photography');
    console.log('Connected to MongoDB');

    // Clear existing data
    await GalleryImage.deleteMany({});
    await Service.deleteMany({});
    await Testimonial.deleteMany({});
    console.log('Cleared existing data');

    // Insert sample data
    await GalleryImage.insertMany(sampleGalleryImages);
    console.log('Inserted gallery images');

    await Service.insertMany(sampleServices);
    console.log('Inserted services');

    await Testimonial.insertMany(sampleTestimonials);
    console.log('Inserted testimonials');

    console.log('Database seeded successfully!');
    
    // Generate admin password hash
    const adminPassword = 'admin123'; // Change this!
    const hashedPassword = await bcrypt.hash(adminPassword, 12);
    console.log('\n--- ADMIN SETUP ---');
    console.log('Add this to your .env.local file:');
    console.log(`ADMIN_PASSWORD_HASH=${hashedPassword}`);
    console.log('Default admin password:', adminPassword);
    console.log('IMPORTANT: Change the admin password after first login!');

  } catch (error) {
    console.error('Error seeding database:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the seeding function
seedDatabase();
