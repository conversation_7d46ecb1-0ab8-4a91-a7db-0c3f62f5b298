import Image from 'next/image';
import { Camera, Heart, Award, Users, Clock, MapPin } from 'lucide-react';

export const metadata = {
  title: 'About - Eternal Moments Wedding Photography',
  description: 'Meet <PERSON>, your wedding photographer. Learn about her passion for capturing love stories and her approach to wedding photography.',
};

const stats = [
  { icon: Camera, label: 'Weddings Captured', value: '200+' },
  { icon: Heart, label: 'Happy Couples', value: '200+' },
  { icon: Award, label: 'Awards Won', value: '15+' },
  { icon: Clock, label: 'Years Experience', value: '8+' },
];

const values = [
  {
    title: 'Authentic Storytelling',
    description: 'I believe in capturing genuine moments and emotions, not just posed shots. Your story unfolds naturally, and I\'m there to preserve it.',
  },
  {
    title: 'Timeless Elegance',
    description: 'My editing style focuses on creating images that will look beautiful decades from now, with classic tones and careful attention to detail.',
  },
  {
    title: 'Personal Connection',
    description: 'Getting to know you as a couple is essential. I take time to understand your vision and make you feel comfortable throughout the process.',
  },
  {
    title: 'Professional Excellence',
    description: 'From the initial consultation to final delivery, I maintain the highest standards of professionalism and quality in every aspect of my work.',
  },
];

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-96 flex items-center justify-center overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
          }}
        />
        <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8">
          <h1 className="font-serif text-4xl sm:text-5xl lg:text-6xl mb-4">
            About Sarah
          </h1>
          <p className="text-xl sm:text-2xl font-light max-w-2xl mx-auto">
            Your wedding photographer & storyteller
          </p>
        </div>
      </section>

      {/* Main About Section */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-16">
            {/* Image */}
            <div className="relative">
              <div className="relative aspect-[4/5] rounded-lg overflow-hidden shadow-2xl">
                <Image
                  src="https://images.unsplash.com/photo-1494790108755-2616c9c0e8e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Sarah, wedding photographer"
                  fill
                  className="object-cover"
                />
              </div>
              <div className="absolute -bottom-6 -right-6 bg-primary-500 rounded-full p-6 shadow-lg">
                <Camera className="h-8 w-8 text-white" />
              </div>
            </div>

            {/* Content */}
            <div>
              <h2 className="font-serif text-4xl lg:text-5xl mb-6 text-gray-900">
                Hi, I'm Sarah
              </h2>
              <div className="w-24 h-1 bg-primary-500 mb-8"></div>
              
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                Welcome to my world of wedding photography! I'm Sarah, and I've been capturing 
                love stories for over 8 years. What started as a passion project has grown into 
                my life's work – preserving the most precious moments of couples' lives.
              </p>
              
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                My journey began when I photographed my best friend's wedding. Seeing the joy 
                on her face when she received her photos made me realize the incredible power 
                of photography to freeze time and preserve emotions forever.
              </p>
              
              <p className="text-lg text-gray-600 mb-8 leading-relaxed">
                Today, I specialize in romantic, timeless wedding photography that tells your 
                unique story. I believe every couple deserves to have their love documented 
                beautifully, authentically, and with the care it deserves.
              </p>

              <div className="flex items-center space-x-4">
                <MapPin className="h-5 w-5 text-primary-600" />
                <span className="text-gray-600">Based in Wedding City, serving nationwide</span>
              </div>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-100 rounded-full mb-4">
                    <IconComponent className="h-8 w-8 text-primary-600" />
                  </div>
                  <div className="text-3xl font-bold text-primary-600 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="font-serif text-4xl lg:text-5xl mb-4 text-gray-900">
              My Approach
            </h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              These core values guide every wedding I photograph and every relationship I build with my couples.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {values.map((value, index) => (
              <div key={index} className="card p-8">
                <h3 className="font-serif text-2xl font-semibold mb-4 text-gray-900">
                  {value.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {value.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Personal Touch Section */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="font-serif text-4xl lg:text-5xl mb-8 text-gray-900">
              Beyond the Camera
            </h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-8"></div>
            
            <p className="text-lg text-gray-600 mb-6 leading-relaxed">
              When I'm not behind the camera, you'll find me exploring new coffee shops with my husband Mark, 
              hiking with our golden retriever Luna, or planning our next travel adventure. I believe that 
              living life fully helps me capture it better for others.
            </p>
            
            <p className="text-lg text-gray-600 mb-8 leading-relaxed">
              I'm also passionate about mentoring new photographers and giving back to the community. 
              I regularly volunteer my services for non-profit events and teach photography workshops 
              for aspiring artists.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/contact"
                className="btn-primary text-lg px-8 py-4 hover:scale-105 transform transition-all duration-200"
              >
                Let's Connect
              </a>
              <a
                href="/portfolio"
                className="btn-secondary text-lg px-8 py-4 hover:scale-105 transform transition-all duration-200"
              >
                View My Work
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
