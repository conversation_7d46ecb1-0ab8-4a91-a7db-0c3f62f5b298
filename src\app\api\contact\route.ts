import { NextRequest, NextResponse } from 'next/server';
import connectDB from '@/lib/mongodb';
import Contact from '@/models/Contact';
import nodemailer from 'nodemailer';

// Configure nodemailer
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_HOST,
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false,
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
});

export async function POST(request: NextRequest) {
  try {
    await connectDB();

    const body = await request.json();
    const { name, email, phone, weddingDate, venue, guestCount, budget, message } = body;

    // Validate required fields
    if (!name || !email || !message) {
      return NextResponse.json(
        { error: 'Name, email, and message are required' },
        { status: 400 }
      );
    }

    // Create contact entry
    const contact = new Contact({
      name,
      email,
      phone,
      weddingDate: weddingDate ? new Date(weddingDate) : undefined,
      venue,
      guestCount,
      budget,
      message,
    });

    await contact.save();

    // Send email notification
    try {
      await transporter.sendMail({
        from: process.env.SMTP_FROM || '<EMAIL>',
        to: process.env.CONTACT_EMAIL || '<EMAIL>',
        subject: 'New Wedding Photography Inquiry',
        html: `
          <h2>New Contact Form Submission</h2>
          <p><strong>Name:</strong> ${name}</p>
          <p><strong>Email:</strong> ${email}</p>
          ${phone ? `<p><strong>Phone:</strong> ${phone}</p>` : ''}
          ${weddingDate ? `<p><strong>Wedding Date:</strong> ${new Date(weddingDate).toLocaleDateString()}</p>` : ''}
          ${venue ? `<p><strong>Venue:</strong> ${venue}</p>` : ''}
          ${guestCount ? `<p><strong>Guest Count:</strong> ${guestCount}</p>` : ''}
          ${budget ? `<p><strong>Budget:</strong> ${budget}</p>` : ''}
          <p><strong>Message:</strong></p>
          <p>${message.replace(/\n/g, '<br>')}</p>
        `,
      });
    } catch (emailError) {
      console.error('Failed to send email:', emailError);
      // Don't fail the request if email fails
    }

    return NextResponse.json(
      { message: 'Contact form submitted successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { error: 'Failed to submit contact form' },
      { status: 500 }
    );
  }
}
