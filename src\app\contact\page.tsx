import ContactForm from '@/components/contact/ContactForm';
import { Mail, Phone, MapPin, Clock, Instagram, Facebook } from 'lucide-react';

export const metadata = {
  title: 'Contact - Eternal Moments Wedding Photography',
  description: 'Get in touch to discuss your wedding photography needs. Book a consultation and let\'s create something beautiful together.',
};

const contactInfo = [
  {
    icon: Mail,
    label: 'Email',
    value: '<EMAIL>',
    href: 'mailto:<EMAIL>'
  },
  {
    icon: Phone,
    label: 'Phone',
    value: '(*************',
    href: 'tel:+1234567890'
  },
  {
    icon: MapPin,
    label: 'Location',
    value: 'Wedding City, WC 12345',
    href: null
  },
  {
    icon: Clock,
    label: 'Response Time',
    value: 'Within 24 hours',
    href: null
  }
];

const faqs = [
  {
    question: 'How far in advance should I book?',
    answer: 'I recommend booking 6-12 months in advance, especially for popular wedding dates. However, I sometimes have availability for shorter notice bookings.'
  },
  {
    question: 'Do you travel for weddings?',
    answer: 'Absolutely! I love destination weddings and am available for travel anywhere. Travel fees may apply depending on the location.'
  },
  {
    question: 'How many photos will I receive?',
    answer: 'This varies by package, but typically you\'ll receive 75-100 photos per hour of coverage, all professionally edited and high-resolution.'
  },
  {
    question: 'When will I receive my photos?',
    answer: 'Full wedding galleries are delivered within 6-8 weeks. Engagement sessions and smaller shoots are typically ready within 2 weeks.'
  },
  {
    question: 'Can we meet before booking?',
    answer: 'Of course! I offer complimentary consultations via video call or in-person (if local) to discuss your vision and see if we\'re a good fit.'
  }
];

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-96 flex items-center justify-center overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1511285560929-80b456fea0bc?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
          }}
        />
        <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8">
          <h1 className="font-serif text-4xl sm:text-5xl lg:text-6xl mb-4">
            Let's Connect
          </h1>
          <p className="text-xl sm:text-2xl font-light max-w-2xl mx-auto">
            Ready to start planning your perfect wedding photography experience?
          </p>
        </div>
      </section>

      {/* Contact Form & Info */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            {/* Contact Form */}
            <div className="lg:col-span-2">
              <div className="card p-8">
                <h2 className="font-serif text-3xl mb-6 text-gray-900">
                  Tell Me About Your Day
                </h2>
                <p className="text-gray-600 mb-8">
                  I'd love to hear about your vision and how I can help bring it to life. 
                  Fill out the form below and I'll get back to you within 24 hours.
                </p>
                <ContactForm />
              </div>
            </div>

            {/* Contact Info */}
            <div className="space-y-8">
              <div className="card p-6">
                <h3 className="font-serif text-2xl mb-6 text-gray-900">
                  Get in Touch
                </h3>
                <div className="space-y-4">
                  {contactInfo.map((item, index) => {
                    const IconComponent = item.icon;
                    const content = (
                      <div className="flex items-start space-x-3">
                        <IconComponent className="h-5 w-5 text-primary-600 mt-1 flex-shrink-0" />
                        <div>
                          <div className="font-medium text-gray-900">{item.label}</div>
                          <div className="text-gray-600">{item.value}</div>
                        </div>
                      </div>
                    );

                    return item.href ? (
                      <a
                        key={index}
                        href={item.href}
                        className="block hover:text-primary-600 transition-colors"
                      >
                        {content}
                      </a>
                    ) : (
                      <div key={index}>{content}</div>
                    );
                  })}
                </div>

                <div className="mt-8 pt-6 border-t border-gray-200">
                  <h4 className="font-medium text-gray-900 mb-4">Follow Along</h4>
                  <div className="flex space-x-4">
                    <a
                      href="https://instagram.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-primary-600 transition-colors"
                    >
                      <Instagram className="h-6 w-6" />
                    </a>
                    <a
                      href="https://facebook.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-gray-400 hover:text-primary-600 transition-colors"
                    >
                      <Facebook className="h-6 w-6" />
                    </a>
                  </div>
                </div>
              </div>

              {/* Quick Links */}
              <div className="card p-6">
                <h3 className="font-serif text-2xl mb-4 text-gray-900">
                  Quick Links
                </h3>
                <div className="space-y-3">
                  <a
                    href="/services"
                    className="block text-gray-600 hover:text-primary-600 transition-colors"
                  >
                    View Packages & Pricing
                  </a>
                  <a
                    href="/portfolio"
                    className="block text-gray-600 hover:text-primary-600 transition-colors"
                  >
                    Browse Portfolio
                  </a>
                  <a
                    href="/about"
                    className="block text-gray-600 hover:text-primary-600 transition-colors"
                  >
                    Learn About Sarah
                  </a>
                  <a
                    href="/testimonials"
                    className="block text-gray-600 hover:text-primary-600 transition-colors"
                  >
                    Read Client Reviews
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="font-serif text-4xl lg:text-5xl mb-4 text-gray-900">
              Frequently Asked Questions
            </h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Here are answers to some common questions. Don't see yours? Feel free to ask!
            </p>
          </div>

          <div className="max-w-4xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {faqs.map((faq, index) => (
                <div key={index} className="card p-6">
                  <h3 className="font-serif text-xl font-semibold mb-3 text-gray-900">
                    {faq.question}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
