'use client';

import { useState } from 'react';
import { Metadata } from 'next';
import ImageGallery from '@/components/portfolio/ImageGallery';

const categories = [
  { id: 'all', name: 'All Photos', count: 150 },
  { id: 'ceremony', name: 'Ceremony', count: 45 },
  { id: 'reception', name: 'Reception', count: 38 },
  { id: 'portraits', name: 'Portraits', count: 32 },
  { id: 'details', name: 'Details', count: 25 },
  { id: 'engagement', name: 'Engagement', count: 10 },
];

export default function PortfolioPage() {
  const [selectedCategory, setSelectedCategory] = useState('all');

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-96 flex items-center justify-center overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1606216794074-735e91aa2c92?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
          }}
        />
        <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8">
          <h1 className="font-serif text-4xl sm:text-5xl lg:text-6xl mb-4">
            Our Portfolio
          </h1>
          <p className="text-xl sm:text-2xl font-light max-w-2xl mx-auto">
            A collection of love stories beautifully captured
          </p>
        </div>
      </section>

      {/* Category Filter */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
                  selectedCategory === category.id
                    ? 'bg-primary-500 text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-primary-50 hover:text-primary-600 shadow-md'
                }`}
              >
                {category.name}
                <span className="ml-2 text-sm opacity-75">({category.count})</span>
              </button>
            ))}
          </div>

          {/* Gallery */}
          <ImageGallery selectedCategory={selectedCategory} />
        </div>
      </section>

      {/* Call to Action */}
      <section className="section-padding bg-primary-50">
        <div className="container-custom text-center">
          <h2 className="font-serif text-3xl lg:text-4xl mb-4 text-gray-900">
            Ready to Create Your Own Story?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Let's discuss how we can capture the magic of your special day with the same 
            care and artistry you see in our portfolio.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="btn-primary text-lg px-8 py-4 hover:scale-105 transform transition-all duration-200"
            >
              Book Your Session
            </a>
            <a
              href="/services"
              className="btn-secondary text-lg px-8 py-4 hover:scale-105 transform transition-all duration-200"
            >
              View Packages
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
