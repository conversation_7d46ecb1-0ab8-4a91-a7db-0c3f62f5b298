import Link from 'next/link';
import { Check, Star, Camera, Users, Clock, Heart, Gift, MapPin } from 'lucide-react';

export const metadata = {
  title: 'Services & Pricing - Eternal Moments Wedding Photography',
  description: 'Explore our wedding photography packages and services. From intimate elopements to grand celebrations, we have the perfect package for your special day.',
};

const packages = [
  {
    icon: Camera,
    name: 'Full Wedding Day',
    price: 'From $2,500',
    duration: '8-10 hours',
    description: 'Complete wedding day coverage from getting ready to reception',
    features: [
      'Complimentary engagement session',
      '500+ professionally edited high-resolution photos',
      'Online gallery with download & sharing options',
      'Print release for personal use',
      'Second photographer included',
      'USB drive with all images',
      'Timeline planning consultation',
      '6-8 week delivery'
    ],
    popular: true,
    color: 'primary'
  },
  {
    icon: Users,
    name: 'Elopement Package',
    price: 'From $1,200',
    duration: '3-4 hours',
    description: 'Perfect for intimate ceremonies and small celebrations',
    features: [
      'Intimate ceremony coverage',
      '150+ professionally edited photos',
      'Online gallery with sharing options',
      'Print release included',
      'Location scouting assistance',
      'Quick turnaround (1 week)',
      'Travel within 50 miles included'
    ],
    popular: false,
    color: 'blush'
  },
  {
    icon: Clock,
    name: 'Engagement Session',
    price: 'From $450',
    duration: '1-2 hours',
    description: 'Capture your love story before the big day',
    features: [
      'Location of your choice',
      '75+ professionally edited photos',
      'Online gallery for sharing',
      'Perfect for save-the-dates',
      'One outfit change included',
      '2 week delivery',
      'Print release included'
    ],
    popular: false,
    color: 'gold'
  }
];

const addOns = [
  {
    icon: Gift,
    name: 'Wedding Album',
    price: 'From $650',
    description: 'Premium leather-bound album with your favorite images'
  },
  {
    icon: Heart,
    name: 'Bridal Session',
    price: 'From $350',
    description: 'Solo bridal portraits in your wedding dress'
  },
  {
    icon: MapPin,
    name: 'Destination Wedding',
    price: 'Custom Quote',
    description: 'Travel photography for destination weddings'
  }
];

export default function ServicesPage() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-96 flex items-center justify-center overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1465495976277-4387d4b0e4a6?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
          }}
        />
        <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8">
          <h1 className="font-serif text-4xl sm:text-5xl lg:text-6xl mb-4">
            Services & Pricing
          </h1>
          <p className="text-xl sm:text-2xl font-light max-w-2xl mx-auto">
            Thoughtfully crafted packages for every love story
          </p>
        </div>
      </section>

      {/* Packages Section */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="font-serif text-4xl lg:text-5xl mb-4 text-gray-900">
              Photography Packages
            </h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Each package is designed to capture your unique story with the care and artistry it deserves.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-16">
            {packages.map((pkg, index) => {
              const IconComponent = pkg.icon;
              return (
                <div
                  key={pkg.name}
                  className={`relative card p-8 hover:scale-105 transform transition-all duration-300 ${
                    pkg.popular ? 'ring-2 ring-primary-500 shadow-xl' : ''
                  }`}
                >
                  {pkg.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <div className="bg-primary-500 text-white px-4 py-2 rounded-full text-sm font-medium flex items-center">
                        <Star className="h-4 w-4 mr-1" />
                        Most Popular
                      </div>
                    </div>
                  )}
                  
                  <div className="text-center mb-6">
                    <div className={`inline-flex items-center justify-center w-16 h-16 bg-${pkg.color}-100 rounded-full mb-4`}>
                      <IconComponent className={`h-8 w-8 text-${pkg.color}-600`} />
                    </div>
                    <h3 className="font-serif text-2xl font-semibold mb-2 text-gray-900">
                      {pkg.name}
                    </h3>
                    <div className="text-3xl font-bold text-primary-600 mb-1">
                      {pkg.price}
                    </div>
                    <div className="text-gray-500 mb-4">{pkg.duration}</div>
                    <p className="text-gray-600">{pkg.description}</p>
                  </div>

                  <ul className="space-y-3 mb-8">
                    {pkg.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start">
                        <Check className="h-5 w-5 text-primary-500 mr-3 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-600">{feature}</span>
                      </li>
                    ))}
                  </ul>

                  <Link
                    href="/contact"
                    className={`w-full text-center block py-3 px-6 rounded-lg font-medium transition-colors duration-200 ${
                      pkg.popular
                        ? 'bg-primary-500 hover:bg-primary-600 text-white'
                        : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                    }`}
                  >
                    Book This Package
                  </Link>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Add-ons Section */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="font-serif text-4xl lg:text-5xl mb-4 text-gray-900">
              Add-On Services
            </h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Enhance your photography experience with these additional services.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
            {addOns.map((addon, index) => {
              const IconComponent = addon.icon;
              return (
                <div key={addon.name} className="card p-6 text-center">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-primary-100 rounded-full mb-4">
                    <IconComponent className="h-6 w-6 text-primary-600" />
                  </div>
                  <h3 className="font-serif text-xl font-semibold mb-2 text-gray-900">
                    {addon.name}
                  </h3>
                  <div className="text-2xl font-bold text-primary-600 mb-3">
                    {addon.price}
                  </div>
                  <p className="text-gray-600">{addon.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="font-serif text-4xl lg:text-5xl mb-4 text-gray-900">
              The Experience
            </h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              From our first conversation to your final gallery delivery, here's what you can expect.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                step: '01',
                title: 'Initial Consultation',
                description: 'We\'ll chat about your vision, timeline, and how I can best serve you.'
              },
              {
                step: '02',
                title: 'Contract & Planning',
                description: 'Secure your date and begin planning the perfect timeline for your day.'
              },
              {
                step: '03',
                title: 'Your Wedding Day',
                description: 'Relax and enjoy while I capture every precious moment of your celebration.'
              },
              {
                step: '04',
                title: 'Gallery Delivery',
                description: 'Receive your beautifully edited photos in a private online gallery.'
              }
            ].map((item, index) => (
              <div key={index} className="text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-primary-500 text-white rounded-full text-2xl font-bold mb-4">
                  {item.step}
                </div>
                <h3 className="font-serif text-xl font-semibold mb-3 text-gray-900">
                  {item.title}
                </h3>
                <p className="text-gray-600">{item.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-primary-50">
        <div className="container-custom text-center">
          <h2 className="font-serif text-3xl lg:text-4xl mb-4 text-gray-900">
            Ready to Begin Your Story?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Let's schedule a consultation to discuss your vision and create the perfect 
            photography experience for your special day.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/contact"
              className="btn-primary text-lg px-8 py-4 hover:scale-105 transform transition-all duration-200"
            >
              Book Consultation
            </Link>
            <Link
              href="/portfolio"
              className="btn-secondary text-lg px-8 py-4 hover:scale-105 transform transition-all duration-200"
            >
              View Portfolio
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
