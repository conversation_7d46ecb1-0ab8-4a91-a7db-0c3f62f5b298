'use client';

import { useState, useEffect } from 'react';
import { Star, Quote, Heart, Calendar } from 'lucide-react';

interface Testimonial {
  id: string;
  name: string;
  message: string;
  rating: number;
  weddingDate?: string;
  createdAt: string;
}

// Mock data - in real app, this would come from API
const mockTestimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON> & <PERSON>',
    message: '<PERSON> captured our wedding day so beautifully! Every photo tells a story and brings back all the emotions from that perfect day. Her attention to detail and ability to capture candid moments is incredible. We couldn\'t be happier with our photos!',
    rating: 5,
    weddingDate: '2023-09-15',
    createdAt: '2023-09-20'
  },
  {
    id: '2',
    name: '<PERSON> & <PERSON>',
    message: 'Working with <PERSON> was an absolute dream. She made us feel so comfortable during our engagement session and wedding day. The photos are stunning and we\'ve received so many compliments. Thank you for capturing our love story so perfectly!',
    rating: 5,
    weddingDate: '2023-07-22',
    createdAt: '2023-07-28'
  },
  {
    id: '3',
    name: '<PERSON> & <PERSON>',
    message: '<PERSON> is not just a photographer, she\'s an artist. Our wedding photos are like pieces of art that we\'ll treasure forever. Her style is exactly what we were looking for - romantic, timeless, and elegant. Highly recommend!',
    rating: 5,
    weddingDate: '2023-06-10',
    createdAt: '2023-06-18'
  },
  {
    id: '4',
    name: 'Anna & Tom',
    message: 'From our first meeting to receiving our final gallery, Sarah exceeded all our expectations. She was professional, creative, and so easy to work with. Our photos are absolutely gorgeous and capture every special moment from our day.',
    rating: 5,
    weddingDate: '2023-05-05',
    createdAt: '2023-05-12'
  },
  {
    id: '5',
    name: 'Rachel & Chris',
    message: 'Sarah has such a gift for capturing authentic emotions. Looking at our photos brings tears to my eyes every time. She documented our day so beautifully and we have memories that will last a lifetime. Thank you, Sarah!',
    rating: 5,
    weddingDate: '2023-04-20',
    createdAt: '2023-04-28'
  },
  {
    id: '6',
    name: 'Jessica & Mark',
    message: 'We were so nervous about having our photos taken, but Sarah made us feel completely at ease. She guided us through poses naturally and captured our personalities perfectly. The results speak for themselves - we love every single photo!',
    rating: 5,
    weddingDate: '2023-03-18',
    createdAt: '2023-03-25'
  }
];

export default function TestimonialsPage() {
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      setTestimonials(mockTestimonials);
      setLoading(false);
    }, 1000);
  }, []);

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-5 w-5 ${
          index < rating ? 'text-gold-400 fill-current' : 'text-gray-300'
        }`}
      />
    ));
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <section className="relative h-96 flex items-center justify-center overflow-hidden">
          <div
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1519741497674-611481863552?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
            }}
          />
          <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8">
            <h1 className="font-serif text-4xl sm:text-5xl lg:text-6xl mb-4">
              Client Love
            </h1>
            <p className="text-xl sm:text-2xl font-light max-w-2xl mx-auto">
              What couples are saying about their experience
            </p>
          </div>
        </section>

        <section className="section-padding">
          <div className="container-custom">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, index) => (
                <div key={index} className="card p-6">
                  <div className="animate-pulse">
                    <div className="flex space-x-1 mb-4">
                      {[...Array(5)].map((_, i) => (
                        <div key={i} className="h-5 w-5 bg-gray-200 rounded"></div>
                      ))}
                    </div>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded mb-4"></div>
                    <div className="h-6 bg-gray-200 rounded"></div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="relative h-96 flex items-center justify-center overflow-hidden">
        <div
          className="absolute inset-0 bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1519741497674-611481863552?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80')`,
          }}
        />
        <div className="relative z-10 text-center text-white px-4 sm:px-6 lg:px-8">
          <h1 className="font-serif text-4xl sm:text-5xl lg:text-6xl mb-4">
            Client Love
          </h1>
          <p className="text-xl sm:text-2xl font-light max-w-2xl mx-auto">
            What couples are saying about their experience
          </p>
        </div>
      </section>

      {/* Stats Section */}
      <section className="section-padding bg-primary-50">
        <div className="container-custom">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-primary-600 mb-2">200+</div>
              <div className="text-gray-600">Happy Couples</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary-600 mb-2">5.0</div>
              <div className="text-gray-600">Average Rating</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary-600 mb-2">100%</div>
              <div className="text-gray-600">Would Recommend</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-primary-600 mb-2">8+</div>
              <div className="text-gray-600">Years Experience</div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Grid */}
      <section className="section-padding">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="font-serif text-4xl lg:text-5xl mb-4 text-gray-900">
              Real Stories from Real Couples
            </h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Nothing makes me happier than hearing from couples about their experience. 
              Here's what some of my recent clients have shared.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className="card p-6 hover:shadow-xl transition-shadow duration-300"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex space-x-1">
                    {renderStars(testimonial.rating)}
                  </div>
                  <Quote className="h-6 w-6 text-primary-300" />
                </div>
                
                <p className="text-gray-600 mb-6 leading-relaxed">
                  "{testimonial.message}"
                </p>
                
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-semibold text-gray-900">{testimonial.name}</div>
                    {testimonial.weddingDate && (
                      <div className="text-sm text-gray-500 flex items-center mt-1">
                        <Calendar className="h-4 w-4 mr-1" />
                        {new Date(testimonial.weddingDate).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </div>
                    )}
                  </div>
                  <Heart className="h-5 w-5 text-blush-400" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="section-padding bg-gray-50">
        <div className="container-custom text-center">
          <h2 className="font-serif text-3xl lg:text-4xl mb-4 text-gray-900">
            Ready to Create Your Own Love Story?
          </h2>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Join the hundreds of couples who have trusted me to capture their most precious moments. 
            Let's create something beautiful together.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/contact"
              className="btn-primary text-lg px-8 py-4 hover:scale-105 transform transition-all duration-200"
            >
              Start Your Journey
            </a>
            <a
              href="/portfolio"
              className="btn-secondary text-lg px-8 py-4 hover:scale-105 transform transition-all duration-200"
            >
              View Portfolio
            </a>
          </div>
        </div>
      </section>
    </div>
  );
}
