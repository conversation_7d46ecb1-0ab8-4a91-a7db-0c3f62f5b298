'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import toast from 'react-hot-toast';
import { Send, Calendar, MapPin, Users, DollarSign } from 'lucide-react';

interface ContactFormData {
  name: string;
  email: string;
  phone?: string;
  weddingDate?: string;
  venue?: string;
  guestCount?: number;
  budget?: string;
  message: string;
}

const budgetOptions = [
  { value: 'under-2000', label: 'Under $2,000' },
  { value: '2000-5000', label: '$2,000 - $5,000' },
  { value: '5000-10000', label: '$5,000 - $10,000' },
  { value: '10000-plus', label: '$10,000+' },
  { value: 'not-sure', label: 'Not sure yet' },
];

export default function ContactForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors }
  } = useForm<ContactFormData>();

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        toast.success('Thank you! Your message has been sent successfully. I\'ll get back to you within 24 hours.');
        reset();
      } else {
        const errorData = await response.json();
        toast.error(errorData.error || 'Something went wrong. Please try again.');
      }
    } catch (error) {
      toast.error('Network error. Please check your connection and try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            Name *
          </label>
          <input
            type="text"
            id="name"
            {...register('name', { required: 'Name is required' })}
            className={`input-field ${errors.name ? 'border-red-500' : ''}`}
            placeholder="Your full name"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
            Email *
          </label>
          <input
            type="email"
            id="email"
            {...register('email', {
              required: 'Email is required',
              pattern: {
                value: /^\S+@\S+$/i,
                message: 'Please enter a valid email address'
              }
            })}
            className={`input-field ${errors.email ? 'border-red-500' : ''}`}
            placeholder="<EMAIL>"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
          )}
        </div>
      </div>

      <div>
        <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
          Phone Number
        </label>
        <input
          type="tel"
          id="phone"
          {...register('phone')}
          className="input-field"
          placeholder="(*************"
        />
      </div>

      {/* Wedding Details */}
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <Calendar className="h-5 w-5 mr-2 text-primary-600" />
          Wedding Details
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label htmlFor="weddingDate" className="block text-sm font-medium text-gray-700 mb-2">
              Wedding Date
            </label>
            <input
              type="date"
              id="weddingDate"
              {...register('weddingDate')}
              className="input-field"
            />
          </div>

          <div>
            <label htmlFor="guestCount" className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
              <Users className="h-4 w-4 mr-1" />
              Guest Count
            </label>
            <input
              type="number"
              id="guestCount"
              {...register('guestCount', { min: 1 })}
              className="input-field"
              placeholder="Approximate number"
            />
          </div>
        </div>

        <div className="mt-6">
          <label htmlFor="venue" className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
            <MapPin className="h-4 w-4 mr-1" />
            Venue or Location
          </label>
          <input
            type="text"
            id="venue"
            {...register('venue')}
            className="input-field"
            placeholder="Venue name or general location"
          />
        </div>

        <div className="mt-6">
          <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
            <DollarSign className="h-4 w-4 mr-1" />
            Photography Budget
          </label>
          <select
            id="budget"
            {...register('budget')}
            className="input-field"
          >
            <option value="">Select your budget range</option>
            {budgetOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Message */}
      <div className="border-t border-gray-200 pt-6">
        <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
          Tell me about your vision *
        </label>
        <textarea
          id="message"
          {...register('message', { required: 'Please tell me about your vision' })}
          className={`textarea-field ${errors.message ? 'border-red-500' : ''}`}
          placeholder="Share your story, vision, and any specific requests or questions you have..."
          rows={6}
        />
        {errors.message && (
          <p className="mt-1 text-sm text-red-600">{errors.message.message}</p>
        )}
      </div>

      {/* Submit Button */}
      <div className="pt-6">
        <button
          type="submit"
          disabled={isSubmitting}
          className={`w-full btn-primary text-lg py-4 flex items-center justify-center ${
            isSubmitting ? 'opacity-75 cursor-not-allowed' : 'hover:scale-105 transform transition-all duration-200'
          }`}
        >
          {isSubmitting ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
              Sending...
            </>
          ) : (
            <>
              <Send className="h-5 w-5 mr-2" />
              Send Message
            </>
          )}
        </button>
      </div>

      <p className="text-sm text-gray-500 text-center">
        I typically respond within 24 hours. Can't wait to hear from you!
      </p>
    </form>
  );
}
