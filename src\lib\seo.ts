import { Metadata } from 'next';

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article';
}

const defaultSEO = {
  title: 'Eternal Moments - Wedding Photography',
  description: 'Capturing the magic of your special day with artistic vision and heartfelt storytelling. Professional wedding photography services with a romantic, timeless approach.',
  keywords: 'wedding photography, wedding photographer, bridal photography, engagement photos, wedding portraits, romantic photography, timeless wedding photos',
  image: '/images/og-image.jpg',
  url: 'https://eternalmoments.com',
  type: 'website' as const,
};

export function generateSEO({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website'
}: SEOProps = {}): Metadata {
  const seo = {
    title: title ? `${title} - Eternal Moments` : defaultSEO.title,
    description: description || defaultSEO.description,
    keywords: keywords || defaultSEO.keywords,
    image: image || defaultSEO.image,
    url: url || defaultSEO.url,
    type,
  };

  return {
    title: seo.title,
    description: seo.description,
    keywords: seo.keywords,
    authors: [{ name: 'Sarah - Eternal Moments Photography' }],
    creator: 'Eternal Moments Photography',
    publisher: 'Eternal Moments Photography',
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      type: seo.type,
      locale: 'en_US',
      url: seo.url,
      title: seo.title,
      description: seo.description,
      siteName: 'Eternal Moments Wedding Photography',
      images: [
        {
          url: seo.image,
          width: 1200,
          height: 630,
          alt: seo.title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: seo.title,
      description: seo.description,
      images: [seo.image],
      creator: '@eternalmoments',
    },
    alternates: {
      canonical: seo.url,
    },
  };
}

export const structuredData = {
  '@context': 'https://schema.org',
  '@type': 'LocalBusiness',
  name: 'Eternal Moments Wedding Photography',
  description: 'Professional wedding photography services specializing in romantic, timeless wedding photos.',
  url: 'https://eternalmoments.com',
  telephone: '******-567-8900',
  email: '<EMAIL>',
  address: {
    '@type': 'PostalAddress',
    streetAddress: '123 Photography Lane',
    addressLocality: 'Wedding City',
    addressRegion: 'WC',
    postalCode: '12345',
    addressCountry: 'US',
  },
  geo: {
    '@type': 'GeoCoordinates',
    latitude: '40.7128',
    longitude: '-74.0060',
  },
  openingHours: 'Mo-Fr 09:00-18:00',
  priceRange: '$1200-$5000',
  serviceArea: {
    '@type': 'GeoCircle',
    geoMidpoint: {
      '@type': 'GeoCoordinates',
      latitude: '40.7128',
      longitude: '-74.0060',
    },
    geoRadius: '100',
  },
  hasOfferCatalog: {
    '@type': 'OfferCatalog',
    name: 'Wedding Photography Services',
    itemListElement: [
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Full Wedding Day Photography',
          description: 'Complete wedding day coverage from getting ready to reception',
        },
        price: '2500',
        priceCurrency: 'USD',
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Elopement Photography',
          description: 'Intimate ceremony coverage for small celebrations',
        },
        price: '1200',
        priceCurrency: 'USD',
      },
      {
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: 'Engagement Session',
          description: 'Pre-wedding couple photography session',
        },
        price: '450',
        priceCurrency: 'USD',
      },
    ],
  },
  aggregateRating: {
    '@type': 'AggregateRating',
    ratingValue: '5.0',
    reviewCount: '200',
    bestRating: '5',
    worstRating: '1',
  },
  review: [
    {
      '@type': 'Review',
      author: {
        '@type': 'Person',
        name: 'Sarah & Michael',
      },
      reviewRating: {
        '@type': 'Rating',
        ratingValue: '5',
        bestRating: '5',
      },
      reviewBody: 'Sarah captured our wedding day so beautifully! Every photo tells a story and brings back all the emotions from that perfect day.',
    },
  ],
  sameAs: [
    'https://www.instagram.com/eternalmoments',
    'https://www.facebook.com/eternalmoments',
  ],
};
